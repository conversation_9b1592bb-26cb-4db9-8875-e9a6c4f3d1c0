<template>
  <view :class="styles.recommend">
    <view :class="styles.title">
      <text :class="styles.line"></text>
      {{ titleText }}
    </view>
    <view :class="styles.list">
      <view 
        :class="styles.item" 
        v-for="(item, index) in list" 
        :key="index"
        @tap="onGoDetail(item)"
      >
        <image :src="item.Image" :class="styles.image" />
        <view :class="styles.info">
          <view :class="styles.name">{{ item.Name }}</view>
          <view :class="styles.price">
            <text>¥{{ item.UnitPrice }}</text>
            <text v-if="item.RetailPrice" :class="styles.oldPrice">¥{{ item.RetailPrice }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import Taro from '@tarojs/taro'
import styles from './index.module.less'

// 定义props
interface Props {
  type?: string // 推荐类型：pt-拼团，yr-优惠，tj-推荐，默认为普通推荐
}

const props = withDefaults(defineProps<Props>(), {
  type: ''
})

// 响应式数据
const list = ref<any[]>([])

// 计算属性
const titleText = computed(() => {
  switch (props.type) {
    case 'pt':
      return '拼团秒杀'
    case 'yr':
      return '限时抢购'
    case 'tj':
      return '为你推荐'
    default:
      return '猜你喜欢'
  }
})

// 方法
const onGoDetail = (item: any) => {
  Taro.navigateTo({
    url: `/pages/proDetail/index?ProductCode=${item.ProductCode}&isService=${item.Product === 'False'}`
  })
}

const getRecommendList = async () => {
  try {
    // 这里应该调用实际的API获取推荐商品
    // 暂时使用模拟数据
    list.value = [
      {
        ProductCode: 'DEMO001',
        Name: '示例商品1',
        Image: '../../static/default.jpg',
        UnitPrice: '99.00',
        RetailPrice: '199.00',
        Product: 'True'
      },
      {
        ProductCode: 'DEMO002',
        Name: '示例商品2',
        Image: '../../static/default.jpg',
        UnitPrice: '159.00',
        RetailPrice: '299.00',
        Product: 'True'
      }
    ]
  } catch (error) {
    console.error('获取推荐商品失败:', error)
  }
}

// 生命周期
onMounted(() => {
  getRecommendList()
})

// 导出组件
export default {
  name: 'Recommend'
}
</script>
