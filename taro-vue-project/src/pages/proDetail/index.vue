<template>
  <!-- 商品详情页 - 从WePY2迁移到Taro + Vue3 -->
  <view :class="styles.container" v-if="completeFetch">
    <!-- 倒计时 - 限时抢购 -->
    <view :class="[styles.countdown, styles.xsCountdown]" v-if="detail.PromoType === 'LT'">
      <text>距离{{ detail.TimeType === 'END' ? '结束' : '开始' }}：</text>
      <nut-countdown
        :end-time="detail.MilliSeconds"
        format="DD 天 HH 时 mm 分 ss 秒"
        @on-end="finish"
      />
    </view>
    
    <!-- 倒计时 - 拼团 -->
    <view :class="styles.countdown" v-if="detail.PromoType === 'GP'">
      <text>距离{{ detail.TimeType === 'END' ? '结束' : '开始' }}：</text>
      <nut-countdown
        :end-time="detail.MilliSeconds"
        format="DD 天 HH 时 mm 分 ss 秒"
        @on-end="finish"
      />
    </view>
    
    <!-- 商品轮播图 -->
    <swiper
      :class="styles.swiper"
      indicator-color="#A1A1A1"
      indicator-active-color="#fff"
      :indicator-dots="true"
      :autoplay="true"
    >
      <swiper-item v-for="(item, index) in background" :key="index">
        <image 
          :src="item.Image" 
          :class="styles.swiperItem" 
          mode="aspectFill"
          @tap="onPreviewImg(item.Image)"
        />
      </swiper-item>
    </swiper>
    
    <!-- 限时抢购样式 -->
    <view :class="styles.pt" v-if="detail.PromoType === 'LT'">
      <view :class="[styles.top, styles.xs]">
        <text :class="styles.xsqgTitle">限时抢购</text>
        <button :class="styles.col2" open-type="share">
          <text>分享</text>
          <image src="../../static/proDetail/share-g.png" :class="styles.icon" />
        </button>
      </view>
      
      <view :class="styles.price">
        <view :class="styles.left">
          <view :class="styles.col1">
            <text style="font-size: 40rpx;margin-right:10rpx">活动价:</text>
            <text v-if="!isPointProduct">¥</text>{{ detail.MemberPrice }}
            <text>{{ isPointProduct ? '积分' : '' }}</text>
          </view>
          <view :class="styles.col2">
            立省
            <text v-if="!isPointProduct">¥</text>{{ detail.CurrentSave }}
          </view>
        </view>
      </view>
      
      <view :class="styles.priceDeposit" v-if="isDeposit">
        <text style="margin-right:10rpx">服务确认金:</text>
        <text>¥</text>{{ detail.Deposit }}
      </view>
      
      <view :class="styles.price2">
        <view v-if="detail.RetailPrice != 0">
          市场价: <text>¥{{ detail.RetailPrice }}</text>
        </view>
        <view>
          会员价: <text>¥{{ detail.CurrentPrice }}</text>
        </view>
      </view>
      
      <view :class="styles.name">
        <view :class="styles.row1">{{ detail.Title }}</view>
        <view :class="styles.row2">{{ detail.SubTitle }}</view>
        <view :class="[styles.row3, styles.xs]">
          <view
            :class="styles.barCon"
            :style="{ borderColor: detail.percent > 0 ? '#48EDC4' : '#4C4C4C' }"
          >
            <view :class="styles.bar" :style="{ width: detail.percent + '%' }"></view>
          </view>
          <text v-if="detail.TimeType === 'START'">暂未开始</text>
          <text style="color: #0b4c5d" v-else>
            已抢{{ detail.SalesQty }}件，仅剩 {{ detail.LeftQty }} 件
          </text>
        </view>
      </view>
      
      <view :class="styles.other">
        <view :class="styles.row">
          补贴积分 <text>{{ detail.MemberPrice }}</text>
        </view>
        <view :class="styles.row">
          {{ isService ? '项目积分(MC)' : '产品积分(MC)' }} 
          <text>{{ FreeProduct === '1' ? '0' : detail.MC || '--' }}</text>
        </view>
      </view>
    </view>
    
    <!-- 拼团样式 -->
    <view :class="styles.pt" v-else-if="detail.PromoType === 'GP'">
      <view :class="styles.top">
        <view :class="styles.col1">
          <image src="../../static/proDetail/pt-title.png" />
          <text v-if="detail.TimeType === 'START'">暂未开始</text>
          <text v-else>
            已抢{{ detail.SalesQty }}件，仅剩 {{ detail.LeftQty }} 件
          </text>
        </view>
        <button :class="styles.col2" open-type="share">
          <text>分享</text>
          <image src="../../static/proDetail/share.png" :class="styles.icon" />
        </button>
      </view>
      
      <view :class="styles.price">
        <view :class="styles.left">
          <view :class="styles.col1">
            <text style="font-size: 40rpx">活动价:</text>
            <text :class="styles.unit">{{ isPointProduct ? '积分' : '¥' }}</text>
            {{ detail.MemberPrice }}
          </view>
          <view :class="styles.col2">
            立省 <text>{{ isPointProduct ? '' : '¥' }}</text>{{ detail.CurrentSave }}
          </view>
        </view>
      </view>
      
      <view :class="styles.priceDeposit" v-if="isDeposit">
        <text style="margin-right:10rpx">服务确认金:</text>
        <text>¥</text>{{ detail.Deposit }}
      </view>
      
      <view :class="styles.price2">
        <view v-if="detail.RetailPrice != 0">
          市场价: <text>¥{{ detail.RetailPrice }}</text>
        </view>
        <view>
          会员价: <text>¥{{ detail.CurrentPrice }}</text>
        </view>
      </view>
      
      <view :class="styles.name">
        <view :class="styles.row1">{{ detail.Title }}</view>
        <view :class="styles.row2">{{ detail.SubTitle }}</view>
        <view :class="styles.row3" style="justify-content: space-between">
          <view
            :class="styles.barCon"
            :style="{ borderColor: detail.percent > 0 ? '#9F2304' : '#4C4C4C' }"
          >
            <view :class="styles.bar" :style="{ width: detail.percent + '%' }"></view>
          </view>
          <view :class="styles.gpNum">
            <view :class="styles.col1">{{ GroupMembers }}人团</view>
            <view :class="styles.col2">
              <image src="../../static/proDetail/xiaoren.png" />
              <image src="../../static/proDetail/xiaoren.png" />
            </view>
          </view>
        </view>
      </view>
      
      <view :class="styles.other">
        <view :class="styles.row">
          补贴积分 <text>{{ detail.MemberPrice }}</text>
        </view>
        <view :class="styles.row">
          {{ isService ? '项目积分(MC)' : '产品积分(MC)' }} 
          <text>{{ FreeProduct === '1' ? '0' : detail.MC || '--' }}</text>
        </view>
      </view>
    </view>
    
    <!-- 默认商品样式 -->
    <view :class="styles.default" v-else>
      <view :class="styles.proName">
        <view :class="styles.left">
          <view>{{ detail.Title }}</view>
          <view :class="styles.des">{{ detail.SubTitle }}</view>
        </view>
        <view :class="styles.right" open-type="share">
          <text :class="styles.text">分享</text>
          <image src="../../static/proDetail/share-g.png" :class="styles.icon" />
        </view>
      </view>
      
      <view :class="styles.info">
        <!-- 服务确认金价格 -->
        <view :class="styles.servicePrice" v-if="isDeposit">
          <view :class="styles.row1">
            <view :class="styles.col1">
              <view :class="styles.val">
                <text>¥</text>{{ detail.Deposit }}
              </view>
              <view :class="styles.tag">服务确认金</view>
            </view>
            <view :class="styles.col2">
              <view :class="styles.vip">
                <view :class="styles.val">
                  <text>¥</text>{{ detail.MemberPrice }}
                </view>
                <view :class="styles.tag">会员价</view>
              </view>
              <view :class="styles.normal">
                <view :class="styles.val">
                  <text>¥</text>{{ detail.RetailPrice }}
                </view>
                <view :class="styles.tag">市场价</view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 折扣价格 -->
        <view :class="styles.discountPrice" v-else-if="detail.PromoType === 'RB'">
          <view :class="styles.price">
            <text :class="styles.name">活动价: </text>
            <view :class="styles.val">
              <text>¥</text>{{ detail.MemberPrice }}
            </view>
          </view>
          <view :class="styles.tag">
            立省 <text>¥</text>{{ detail.CurrentSave }}元
          </view>
        </view>
        
        <!-- VIP价格 -->
        <view :class="styles.vipPrice" v-else>
          <view :class="styles.row">
            <text>
              <text v-if="!isPointProduct">¥</text>
              {{ FreeProduct === '1' ? 0.0 : detail.MemberPrice }}
            </text>
            <text :class="styles.tag">
              {{ isPointProduct ? '积分' : '会员价' }}
            </text>
          </view>
          <view :class="styles.row">
            <text v-if="detail.PromoType !== 'RB'" :class="styles.retailPrice">
              <text>¥{{ detail.RetailPrice }}</text>
              <view :class="styles.tag">市场价</view>
            </text>
          </view>
        </view>
        
        <!-- 报单爆品价格 -->
        <view :class="styles.price2" v-if="detail.PromoType === 'RB'">
          <view v-if="detail.RetailPrice != 0">
            市场价:<text>¥{{ detail.RetailPrice }}</text>
          </view>
          <view>
            会员价:<text>¥{{ detail.CurrentPrice }}</text>
          </view>
        </view>
        
        <!-- 积分商品说明 -->
        <view :class="styles.mcDes" v-if="isPointProduct">
          本品需使用收入积分, 购物积分或充值积分购买
        </view>
        
        <!-- MC积分信息 -->
        <view :class="styles.mc">
          <view :class="styles.row">
            补贴积分 <text>{{ detail.CurrentPrice }}</text>
          </view>
          <view :class="styles.row">
            {{ isService ? '项目积分(MC)' : '产品积分(MC)' }} 
            <text>{{ FreeProduct === '1' ? '0' : detail.MC || '--' }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 商品信息2 -->
    <view :class="styles.info2" v-if="!isServiceProduct">
      <view :class="styles.row">
        <view v-if="TRADE_CATEGORY[detail.TradeCategoryCode]" style="color: #912e16">
          {{ TRADE_CATEGORY[detail.TradeCategoryCode] }}
        </view>
        <view>
          <text>出货仓</text>{{ detail.adddel || '--' }}
        </view>
      </view>
      <view :class="styles.row">
        <view @tap="onFreightTip">
          <text>运费</text>以提交订单运费为准
          <nut-icon name="help" :class="styles.icon" />
        </view>
        <view v-if="detail.TradeCategoryCode === 'TI'" @tap="onTaxTip">
          <text>进口税</text>商品已包税
          <nut-icon name="help" :class="styles.icon" />
        </view>
      </view>
    </view>

    <!-- 拼团列表 -->
    <view :class="styles.ptList" v-if="detail.PromoType === 'GP' && groupList.length">
      <view :class="styles.title">
        <view :class="styles.col1">这些人刚刚发起拼团，立即参与</view>
        <view :class="styles.col2" @tap="groupListShow = true">查看更多></view>
      </view>
      <view :class="styles.con">
        <view
          v-for="(item, i) in displayGroupList"
          :key="i"
          :class="styles.item"
        >
          <view :class="styles.left">
            <image :src="item.AvatarUrl || '../../static/default.jpg'" />
            <text>{{ item.MemberName }}</text>
          </view>
          <view :class="styles.right">
            <button :class="styles.btn2Bg" @tap="onShowPtDes(item.GroupNo)">
              <text>去拼团</text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 规格选择 -->
    <view :class="styles.standar" @tap="onShowSpec">
      <view :class="styles.row1">
        <view :class="styles.key">规格</view>
        <view :class="styles.val">
          <text v-if="skuStr">{{ skuStr }}</text>
          <text v-else style="color: #b8b7b7">请选择商品规格</text>
        </view>
      </view>
      <view :class="styles.row2">
        说明：{{ detail.NoReasonReturn === 'True' ? '' : '不' }}支持7天无理由退货
      </view>
    </view>

    <!-- 商品详情 -->
    <view :class="styles.detail">
      <view :class="styles.title">
        <text :class="styles.line"></text>
        {{ productType === 'package' ? '礼包详情' : isServiceProduct ? '服务详情' : '商品详情' }}
      </view>

      <view :class="styles.tip2">
        <view :class="styles.content" v-if="isServiceProduct">
          本平台为全球全正品, 全项目, 医美共享平台、保证为世界各国会员完成医美和大健康服务的满意交付。
        </view>
        <view :class="styles.content" v-else>
          本网站为全球各国精选名牌产品, 低价至1-5折, 海关每天24小时直接联线, 保证每单正品验货。
        </view>

        <view :class="styles.des" v-if="isServiceProduct">
          <view :class="styles.name">服务说明</view>
          <view>
            1. 由于本平台使用的正品材料紧俏，且受欢迎的项目名医排满而需效长时间排队等待,部份热门项目会发生限购，请会员选中项目后及时下单，防止无法得到及时服务。<br />
            2. 本平台每周均有爆品项目推荐低价至市场的1-2折，请注意关注抢单，防止错过。
          </view>
        </view>
        <view :class="styles.des" v-else>
          <view :class="styles.name">产品上下架说明</view>
          <view>
            1. 由于本平台全球优质名牌正品采购，每2-3天内都会出现众多受欢迎的产品很快售光而下架，且短时间内无法补货，部分产品也会限购数量，建议当会员选中了心仪产品后及时下单和备用，防止断货。<br />
            2. 本平台每2-3天都会有新的名牌产品上架，请注意搜寻，以免错失。
          </view>
        </view>
      </view>

      <view :class="styles.con">
        <rich-text :nodes="detail.ProductDescription" />
      </view>

      <!-- 有效期 -->
      <view :class="styles.expire" v-if="detail.ExpiredDate">
        <view :class="styles.date">有效期至：{{ detail.ExpiredDate }}</view>
        <view :class="styles.des">
          温馨提示：可能会遇因库存批次的变动而导致有效期出现偏差，以实际收到货物的有效期为准，介意慎拍。
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view :class="styles.actions">
      <view :class="styles.left">
        <button open-type="contact" hover-class="hover" show-message-card>
          <image src="../../static/my/kefu.png" style="width: 25px; height: 25px" />
          <view :class="styles.text">客服</view>
        </button>
        <view style="margin-left: 30rpx" @tap="onGoHome">
          <view>
            <image src="../../static/tabBar/tab1-selected.png" style="width: 25px; height: 25px" />
          </view>
          <view :class="styles.text">首页</view>
        </view>
        <view style="margin-left: 30rpx" @tap="onGoCart">
          <view>
            <image src="../../static/tabBar/tab3-selected.png" style="width: 26px; height: 24px" />
          </view>
          <view :class="styles.text">购物车</view>
        </view>
      </view>

      <view :class="styles.right" v-if="detail.TimeType === 'START'">
        <button :class="styles.btn2Bg" @tap="onSubscribe">
          <text>开始通知</text>
        </button>
      </view>
      <view :class="styles.right" v-else-if="detail.Stock <= 0">
        <button :class="styles.btn2Bg" @tap="onSubscribeStock">
          <text>到货通知</text>
        </button>
      </view>
      <view :class="styles.right" v-else-if="detail.PromoType === 'GP'">
        <button :class="styles.btn3Bg" @tap="onShowBuy">
          <text>发起拼团</text>
        </button>
        <button :class="styles.btn2Bg" @tap="onJoinPt">
          <text>立即参团</text>
        </button>
      </view>
      <view :class="styles.right" v-else>
        <button :class="styles.btn3Bg" @tap="onShowCart">
          <text>加入购物车</text>
        </button>
        <button :class="styles.btn2Bg" @tap="onShowBuy">
          <text>{{ detail.PromoType === 'LT' ? '立即抢购' : isDeposit ? '确认金支付' : '立即购买' }}</text>
        </button>
      </view>
    </view>

    <!-- 回到顶部 -->
    <image
      v-if="showToTop"
      src="../../static/home/<USER>"
      :class="styles.topIcon"
      @tap="goTop"
    />

    <!-- SKU选择弹窗 -->
    <nut-popup
      v-model="show"
      position="bottom"
      :style="{ maxHeight: '80%' }"
      round
      @close="show = false"
    >
      <view :class="styles.sku">
        <view :class="styles.skuTop">
          <nut-icon name="close" :class="styles.close" @tap="show = false" />
          <image :src="sku.Image || detail.Image" :class="styles.left" />
          <view :class="styles.right">
            <view :class="styles.skuPrice">
              <view :class="styles.skuPriceItem">
                <view style="color: #428485">
                  <text style="font-size: 24rpx; font-weight: 500" v-if="!isPointProduct">¥</text>
                  <text v-if="freeProduct === '1'">0.00</text>
                  <text style="font-size: 36rpx; font-weight: 500" v-else>{{ selectedSkuPrice }}</text>
                  <text :class="styles.label" v-if="isPointProduct">积分</text>
                </view>
              </view>
              <view :class="styles.mc">
                {{ isServiceProduct ? '项目积分(MC)' : '产品积分(MC)' }}:
                <text v-if="freeProduct === '1'">0</text>
                <text v-else>{{ sku.MC || detail.MC }}</text>
              </view>
            </view>
            <view :class="styles.selected" v-if="options.length > 0">
              已选规格：{{ skuStr || '请选择' }}
            </view>
          </view>
        </view>

        <!-- 规格选择列表 -->
        <view :class="styles.skuList">
          <view
            v-for="(item, i) in options"
            :key="item.SpecificationCode"
            :class="styles.skuItem"
          >
            <view :class="styles.skuItemName">{{ item.SpecificationName }}</view>
            <view :class="styles.skuItemCon">
              <view
                v-for="item2 in item.SpecOption"
                :key="item2.SelectionCode"
                :class="{ active: item2.checked }"
                @tap="onClickSelType(item2.OptionName, i)"
              >
                {{ item2.OptionName }}
                <text style="color: #a92808" v-if="item2.disabled">(缺货)</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 购买数量 -->
        <view :class="styles.num">
          <text style="font-size: 32rpx; color: #393737">购买数量</text>
          <nut-inputnumber
            v-model="selectNum"
            :min="0"
            :max="options.length > 0 ? sku.Stock : detail.Stock"
            :disabled="options.length > 0 ? sku.Stock <= 0 : detail.Stock <= 0"
            @change="onChange"
          />
        </view>

        <!-- 总计 -->
        <view :class="styles.total">
          共{{ selectNum }}件 合计
          <view style="color: #428485; padding: 0 10rpx">
            {{ !isPointProduct ? '¥' : '' }}
            <text style="font-size: 28rpx; font-weight: 500">
              {{ freeProduct === '1' ? '0.00' : selectAmound }}
            </text>
          </view>
          {{ isPointProduct ? '积分' : '' }}
        </view>

        <!-- 操作按钮 -->
        <view :class="styles.skuBtns" v-if="lackSubscribe">
          <button :class="styles.btn2Bg" @tap="onSubscribeStock">
            <text>到货通知</text>
          </button>
        </view>
        <view :class="styles.skuBtns" v-else-if="detail.PromoType === 'GP' && detail.TimeType === 'END'">
          <button :class="styles.btn3Bg" @tap="onGroupBuy(false)">
            <text>{{ operateType === 2 ? '发起拼团' : '确定' }}</text>
          </button>
          <button v-if="operateType === 2" :class="styles.btn2Bg" @tap="onJoinPt">
            <text>立即参团</text>
          </button>
        </view>
        <view :class="styles.skuBtns" v-else>
          <button v-if="operateType === 0 || operateType === 2" :class="styles.btn3Bg" @tap="onAddCart">
            <text>{{ operateType === 2 ? '加入购物车' : '确定' }}</text>
          </button>
          <button v-if="operateType === 1 || operateType === 2" :class="styles.btn2Bg" @tap="onBuy(false)">
            <text>
              {{ operateType === 2
                ? detail.PromoType === 'LT' && detail.TimeType === 'END'
                  ? '立即抢购'
                  : '立即购买'
                : isDeposit ? '确认金支付' : '立即支付'
              }}
            </text>
          </button>
        </view>
      </view>
    </nut-popup>
  </view>
</template>

<script lang="ts" setup>
// 商品详情页逻辑 - 从WePY2迁移到Taro + Vue3
import { ref, computed, onMounted } from 'vue'
import Taro, { useRouter, useDidShow, usePageScroll, useShareAppMessage } from '@tarojs/taro'
import Recommend from '../../components/recommend/index.vue'
import styles from './index.module.less'

// 导入API和常量
import {
  getProDetail,
  getProBanner,
  getProQtyPurchase,
  ProductOptionCountry,
  ProductSpecCombination,
  ProductSKU,
  AddToCart
} from '../../services/api'
import { TRADE_CATEGORY } from '../../constants'

// 定义商品详情类型
interface ProductDetail {
  ProductCode?: string
  Title?: string
  SubTitle?: string
  Image?: string
  ProductDescription?: string
  PointProduct?: string
  FeaturedProduct?: string
  TimeType?: string
  PromoType?: string
  SalesQty?: number
  PromoQty?: number
  LeftQty?: number
  Stock?: number
  MemberPrice?: number
  RetailPrice?: number
  CurrentPrice?: number
  CurrentSave?: number
  Deposit?: number
  MC?: string
  TradeCategoryCode?: string
  adddel?: string
  NoReasonReturn?: string
  ExpiredDate?: string
  percent?: string
  MilliSeconds?: number
  SupplierCode?: string
}

// 页面参数
const router = useRouter()
const { ProductCode, isService, type, FreeProduct, PromoType, ReferNo, ShareGroupNo, GroupMembers } = router.params

// 响应式数据
const completeFetch = ref(false)
const background = ref<any[]>([]) // 商品轮播图
const detail = ref<ProductDetail>({}) // 商品详情
const isServiceProduct = ref(isService === 'true') // 是否是服务商品
const productType = ref(type || '') // 商品类型
const freeProduct = ref(FreeProduct || '0') // 免费商品标识
const groupMembers = ref(GroupMembers || 0) // 成团人数
const finished = ref(false) // 活动是否已结束
const groupList = ref<any[]>([]) // 拼团列表
const options = ref<any[]>([]) // 商品规格选项
const sku = ref<any>({}) // 当前选中的SKU
const selectNum = ref(0) // 选择数量
const show = ref(false) // SKU弹窗显示状态
const showToTop = ref(false) // 回到顶部按钮显示状态
const operateType = ref(0) // 操作类型：0加入购物车，1立即购买，2选择规格
const lackSubscribe = ref(false) // 是否缺货订阅
const groupListShow = ref(false) // 拼团列表弹窗
const groupDetailShow = ref(false) // 拼团详情弹窗
const groupDes = ref<any>({}) // 拼团详情

// 新增缺失的数据
const institution = ref<any>({}) // 机构信息
const doctorList = ref<any[]>([]) // 医生列表
const beforeTips = ref<any[]>([]) // 详情前提示模块
const afterTips = ref<any[]>([]) // 详情后提示模块
const packages = ref<any[]>([]) // 礼包商品内容
const purchase = ref<any[]>([]) // 多件购买时对应的满减价格
const commentList = ref<any[]>([]) // 评价列表
const commentTotal = ref(0) // 评价总数
const countryCode = ref('') // 国家编号

// 计算属性
const isPointProduct = computed(() => {
  return detail.value.PointProduct === 'True'
})

const isDeposit = computed(() => {
  return isServiceProduct.value && detail.value.FeaturedProduct === 'False' && productType.value !== 'vipPackage'
})

// 已选规格商品价格
const selectedSkuPrice = computed(() => {
  if (sku.value.MemberPrice) {
    return isDeposit.value ? sku.value.Deposit : sku.value.MemberPrice
  }
  return isDeposit.value ? detail.value.Deposit : detail.value.MemberPrice
})

// 总金额
const selectAmound = computed(() => {
  const price = selectedSkuPrice.value || 0
  return (selectNum.value * price).toFixed(2)
})

// 已选规格名称
const skuStr = computed(() => {
  const skuNames: string[] = []
  options.value.forEach((item) => {
    item.SpecOption?.forEach((item2: any) => {
      if (item2.checked) {
        skuNames.push(item2.OptionName)
      }
    })
  })
  return skuNames.join(' / ')
})

// 显示的拼团列表（只显示前2个）
const displayGroupList = computed(() => {
  return groupList.value.slice(0, 2)
})

// 方法
const onPreviewImg = (current: string) => {
  Taro.previewImage({
    urls: background.value.map(item => item.Image),
    current
  })
}

const finish = () => {
  finished.value = true
  if (detail.value.TimeType === 'START') {
    productType.value = ''
    getAllData()
  }
}

// 获取所有数据
const getAllData = async () => {
  try {
    Taro.showLoading({ title: '努力加载中...', mask: true })

    // 并行请求商品详情、轮播图和购买价格
    const [detailRes, bannerRes, purchaseRes] = await Promise.all([
      getProDetail({ ProductCode }),
      getProBanner({ ProductCode }),
      getProQtyPurchase({ ProductCode })
    ])

    detail.value = detailRes.data.d[0] || {}
    background.value = bannerRes.data.d || []

    // 处理购买价格
    if (!detail.value.PromoType) {
      purchase.value = purchaseRes.data.d || []
    }

    // 如果没有轮播图，使用商品主图
    if (background.value.length === 0) {
      background.value = [{ Image: detail.value.Image }]
    }

    // 处理商品描述中的图片样式
    if (detail.value.ProductDescription) {
      detail.value.ProductDescription = detail.value.ProductDescription.replace(
        /\<img/gi,
        '<img style="max-width:100%;height:auto" '
      )
    }

    // 计算商品销售百分比
    if (detail.value.PromoType === 'LT' || detail.value.PromoType === 'GP') {
      const salesQty = detail.value.SalesQty || 0
      const promoQty = detail.value.PromoQty || 1
      detail.value.percent = ((salesQty / promoQty) * 100).toFixed(2)
    }

    // 更新库存信息
    if (detail.value.PromoType && detail.value.PromoType !== 'RB') {
      detail.value.Stock = detail.value.LeftQty
    }

    // 设置免费商品类型
    switch (detail.value.PromoType) {
      case 'RB':
        freeProduct.value = '3'
        break
      case 'LT':
        freeProduct.value = '4'
        break
      case 'GP':
        freeProduct.value = '5'
        break
    }

    // 获取服务机构和医生信息（如果是服务商品）
    if (isServiceProduct.value && detail.value.SupplierCode) {
      try {
        // 这里需要实际的API调用，暂时使用模拟数据
        institution.value = { Name: '示例机构', OperationHour: '9:00-18:00', Address: '示例地址', Contact: [{ ContactNo: '123-456-7890' }] }
        doctorList.value = []
      } catch (error) {
        console.error('获取机构信息失败:', error)
      }
    }

    // 获取商品提醒信息
    try {
      // 这里需要实际的API调用，暂时使用模拟数据
      beforeTips.value = []
      afterTips.value = []
    } catch (error) {
      console.error('获取商品提醒失败:', error)
    }

    // 获取拼团信息
    if (detail.value.PromoType === 'GP') {
      try {
        // 这里需要实际的API调用，暂时使用模拟数据
        groupList.value = []
      } catch (error) {
        console.error('获取拼团信息失败:', error)
      }
    }

    // 获取礼包商品信息
    if (productType.value === 'package') {
      try {
        // 这里需要实际的API调用，暂时使用模拟数据
        packages.value = []
      } catch (error) {
        console.error('获取礼包信息失败:', error)
      }
    }

    completeFetch.value = true
  } catch (error) {
    console.error('获取商品详情失败:', error)
    Taro.showToast({ title: '获取商品详情失败', icon: 'none' })
  } finally {
    Taro.hideLoading()
  }

  // 获取商品评论（独立请求）
  try {
    // 这里需要实际的API调用，暂时使用模拟数据
    commentList.value = []
    commentTotal.value = 0
  } catch (error) {
    console.error('获取评论失败:', error)
  }
}

// 显示规格选择弹窗
const onShowSpec = () => {
  if (finished.value) {
    Taro.showToast({ title: '活动已结束～', icon: 'none' })
    return
  }
  if (options.value.length === 0 && selectNum.value === 0) {
    selectNum.value = 1
  }
  operateType.value = 2
  show.value = true
}

// 显示购物车弹窗
const onShowCart = () => {
  if (finished.value) {
    Taro.showToast({ title: '活动已结束～', icon: 'none' })
    return
  }
  if (options.value.length === 0 && selectNum.value === 0) {
    selectNum.value = 1
  }
  operateType.value = 0
  show.value = true
}

// 显示购买弹窗
const onShowBuy = () => {
  if (finished.value) {
    Taro.showToast({ title: '活动已结束～', icon: 'none' })
    return
  }
  if (options.value.length === 0 && selectNum.value === 0) {
    selectNum.value = 1
  }
  operateType.value = 1
  show.value = true
}

// 规格选择
const onClickSelType = (optionName: string, specIndex: number) => {
  // 清除当前规格组的选择
  options.value[specIndex].SpecOption.forEach((item: any) => {
    item.checked = false
  })

  // 设置新选择
  const selectedOption = options.value[specIndex].SpecOption.find((item: any) => item.OptionName === optionName)
  if (selectedOption) {
    selectedOption.checked = true
  }

  // 更新SKU信息
  updateSku()
}

// 更新SKU信息
const updateSku = () => {
  // 这里应该根据选择的规格组合查找对应的SKU
  // 简化处理，使用默认值
  sku.value = {
    ...detail.value,
    Stock: detail.value.Stock || 0
  }
}

// 数量变化
const onChange = (value: number) => {
  selectNum.value = value
}

// 加入购物车
const onAddCart = async () => {
  if (!validateSelection()) return

  try {
    Taro.showLoading({ title: '加入购物车中...', mask: true })

    const params = {
      ProductCode,
      Qty: selectNum.value,
      // 其他参数...
    }

    await AddToCart(params)
    Taro.showToast({ title: '已加入购物车', icon: 'success' })
    show.value = false
  } catch (error) {
    console.error('加入购物车失败:', error)
    Taro.showToast({ title: '加入购物车失败', icon: 'none' })
  } finally {
    Taro.hideLoading()
  }
}

// 立即购买
const onBuy = (isGroup: boolean = false) => {
  if (!validateSelection()) return

  // 跳转到订单确认页
  const url = `/pages/order/index?ProductCode=${ProductCode}&Qty=${selectNum.value}&isGroup=${isGroup}`
  Taro.navigateTo({ url })
}

// 验证选择
const validateSelection = (): boolean => {
  if (selectNum.value <= 0) {
    Taro.showToast({ title: '请选择购买数量', icon: 'none' })
    return false
  }

  if (options.value.length > 0) {
    const hasUnselected = options.value.some(item =>
      !item.SpecOption.some((option: any) => option.checked)
    )
    if (hasUnselected) {
      Taro.showToast({ title: '请选择商品规格', icon: 'none' })
      return false
    }
  }

  return true
}

// 运费提示
const onFreightTip = () => {
  Taro.showModal({
    title: '运费说明',
    content: '运费以提交订单时显示的运费为准，具体运费根据商品重量、配送地址等因素计算。',
    showCancel: false
  })
}

// 进口税提示
const onTaxTip = () => {
  Taro.showModal({
    title: '进口税说明',
    content: '该商品已包含进口税费，无需额外支付关税。',
    showCancel: false
  })
}

// 跳转首页
const onGoHome = () => {
  Taro.switchTab({ url: '/pages/index/index' })
}

// 跳转购物车
const onGoCart = () => {
  Taro.switchTab({ url: '/pages/cart/index' })
}

// 回到顶部
const goTop = () => {
  Taro.pageScrollTo({ scrollTop: 0, duration: 300 })
}

// 拼团相关方法
const onShowPtDes = (groupNo: string) => {
  // 显示拼团详情
  Taro.showLoading({ title: '加载中...', mask: true })
  // 这里应该调用API获取拼团详情
  console.log('显示拼团详情:', groupNo)
  groupDetailShow.value = true
  groupListShow.value = false
  Taro.hideLoading()
}

// 跳转评论页面
const onGoComment = () => {
  Taro.navigateTo({
    url: `/pages/proComment/index?ProductCode=${ProductCode}`
  })
}

// 跳转礼包商品详情
const onGoPackageDetail = (item: any) => {
  Taro.navigateTo({
    url: `/pages/proDetail/index?ProductCode=${item.ProductCode}&isService=${item.Product === 'False'}`
  })
}

// 跳转医生详情
const onGoDocDetail = (item: any) => {
  console.log('跳转医生详情:', item)
  // 这里可以添加跳转到医生详情页的逻辑
}

const onJoinPt = () => {
  if (finished.value) {
    Taro.showToast({ title: '活动已结束～', icon: 'none' })
    return
  }

  if (ShareGroupNo) {
    // 直接参与指定的拼团
    return onShowJoinPt(ShareGroupNo)
  }

  if (groupList.value.length === 0) {
    Taro.showToast({ title: '没有可参与的拼团，请发起拼团', icon: 'none' })
    return
  }

  show.value = false
  groupListShow.value = true
}

const onShowJoinPt = (groupNo: string) => {
  if (finished.value) {
    Taro.showToast({ title: '活动已结束～', icon: 'none' })
    return
  }

  if (options.value.length === 0 && selectNum.value === 0) {
    selectNum.value = 1
  }

  operateType.value = 3
  groupListShow.value = false
  show.value = true
  // 这里应该设置GroupNo用于后续购买
  console.log('参与拼团:', groupNo)
}

const onGroupBuy = (isGroup: boolean) => {
  // 发起拼团
  onBuy(isGroup)
}

// 订阅相关方法
const onSubscribe = () => {
  Taro.showToast({ title: '已订阅开始通知', icon: 'success' })
}

const onSubscribeStock = () => {
  Taro.showToast({ title: '已订阅到货通知', icon: 'success' })
}

// 页面滚动监听
usePageScroll((res) => {
  showToTop.value = res.scrollTop > 500
})

// 页面生命周期
onMounted(() => {
  getAllData()
})

useDidShow(() => {
  getAllData()
})

// 分享功能
useShareAppMessage(() => {
  const memberCode = Taro.getStorageSync('MemberCode')
  const params = {
    ProductCode,
    GroupMembers: groupMembers.value,
    type: productType.value,
    FreeProduct: freeProduct.value,
    PromoType,
    ReferNo
  }

  const url = Object.entries(params).reduce((pre, next) => {
    if (next[1]) return (pre += `&${next[0]}=${next[1]}`)
    else return pre
  }, `pages/proDetail/index?introducerID=${memberCode}&isService=${isServiceProduct.value}`)

  return {
    title: detail.value.Title,
    imageUrl: detail.value.Image,
    path: url
  }
})
</script>
