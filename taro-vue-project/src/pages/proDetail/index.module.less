/* 商品详情页样式模块 - 从WePY项目迁移到Taro + CSS Modules */
@import '../../common/common.less';

/* 页面基础样式 */
:global(page) {
  background-color: #f6f6f6;
}

.container {
  background-color: #f6f6f6;
  min-height: 100vh;
}

/* 自定义弹窗 */
.customDialog {
  position: relative;
  z-index: 999;
}

/* 倒计时样式 */
.countdown {
  height: 66rpx;
  .flex(center, center);
  background-color: #3B6373;
  color: #fff;
  font-size: 33rpx;
  margin: 20rpx 27rpx 10rpx;
  
  :global(.nut-countdown) {
    font-size: 33rpx !important;
    color: #fff !important;
  }
}

.countdown.xsCountdown {
  background-color: #C6FCED;
  border-radius: 30rpx;
  color: #1A1A1A;
  
  :global(.nut-countdown) {
    color: #1A1A1A !important;
  }
}

/* 轮播图样式 */
.swiper {
  position: relative;
  width: 100%;
  height: 750rpx;
  
  .swiperItem {
    width: 100%;
    height: 100%;
  }
}

/* 标题图标 */
.titleIcon {
  width: 243rpx;
  height: 54rpx;
  margin-left: 20rpx;
}

/* 默认商品样式 */
.default {
  .proName {
    .flex();
    padding: 28rpx 20rpx;
    border-bottom: 1rpx solid #f6f6f6;
    background-color: #fff;
    
    .left {
      flex: 1;
      font-size: 35rpx;
      color: #000;
      font-weight: bold;
      
      .des {
        font-size: 24rpx;
        color: #999;
        font-weight: 400;
        .ellipsis();
      }
    }
    
    .right {
      display: flex;
      align-items: center;
      color: #707070;
      margin-left: 54rpx;
      background-color: #fff;
      padding: 0;
      line-height: 30rpx;
      border: none;
      
      .icon {
        width: 34rpx;
        height: 34rpx;
        margin-left: 10rpx;
      }
      
      .text {
        font-size: 28rpx;
      }
    }
  }
  
  .info {
    padding: 20rpx;
    background-color: #fff;
    
    .servicePrice {
      padding-top: 15rpx;
      
      .row1 {
        .flex(center, space-between);
        
        .col1 {
          flex: 2;
          .flex(center, start);
          font-size: 35rpx;
          color: #1f4b38;
          font-weight: bold;
          
          .val {
            font-size: 60rpx;
            color: #428485;
            
            text {
              font-size: 45rpx;
            }
          }
          
          .tag {
            font-size: 24rpx;
            color: #fff;
            text-align: center;
            line-height: 54rpx;
            padding: 0 10rpx;
            border-radius: 8rpx;
            background-color: #428485;
            margin-left: 10rpx;
          }
        }
        
        .col2 {
          flex: 1.5;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          
          .vip {
            .flex(center, start);
            color: #428485;
            font-weight: bold;
            
            .val {
              min-width: 150rpx;
              font-size: 43rpx;
              margin-right: 10rpx;
              
              text {
                font-size: 33rpx;
              }
            }
            
            .tag {
              font-size: 26rpx;
              color: #428485;
              width: 110rpx;
              line-height: 40rpx;
              text-align: center;
              border-radius: 8rpx;
              color: #565959;
              background-color: #E9FDFD;
              margin-left: 10rpx;
            }
          }
          
          .normal {
            .flex(center, start);
            color: #5E5F63;
            font-weight: bold;
            
            .val {
              min-width: 150rpx;
              font-size: 43rpx;
              color: #5d5f63;
              margin-right: 10rpx;
              text-decoration: line-through;
              
              text {
                font-size: 33rpx;
              }
            }
            
            .tag {
              font-size: 26rpx;
              width: 110rpx;
              line-height: 40rpx;
              text-align: center;
              border-radius: 8rpx;
              color: #565959;
              margin-left: 10rpx;
              box-sizing: border-box;
              border: 1rpx solid #565959;
            }
          }
        }
      }
    }
    
    .discountPrice {
      display: flex;
      align-items: center;
      
      .price {
        display: flex;
        align-items: baseline;
        color: #8e2c13;
        font-weight: bold;
        
        .name {
          font-size: 35rpx;
        }
        
        .val {
          font-size: 55rpx;
          
          text {
            font-size: 35rpx;
            font-weight: 400;
          }
        }
      }
      
      .tag {
        padding: 0 13rpx;
        border: 1rpx solid #6d7075;
        border-radius: 12rpx;
        color: #6d7075;
        line-height: 64rpx;
        font-size: 35rpx;
        font-weight: bold;
        margin-left: 20rpx;
        
        text {
          font-size: 26rpx;
          font-weight: 400;
        }
      }
    }
    
    .vipPrice {
      display: flex;
      align-items: baseline;
      font-size: 40rpx;
      font-weight: bold;
      color: #428485;
      
      .row {
        display: flex;
        align-items: center;
        flex: 2;
        
        &:nth-child(2) {
          flex: 1.5;
        }
      }
      
      text {
        font-size: 28rpx;
      }
      
      .tag {
        width: 118rpx;
        line-height: 46rpx;
        background: #428485;
        text-align: center;
        border-radius: 10rpx;
        color: #fff;
        font-size: 30rpx;
        margin-left: 19rpx;
      }
      
      .retailPrice {
        display: flex;
        align-items: center;
        color: #5d5f63;
        
        text {
          text-decoration: line-through;
        }
        
        .tag {
          background-color: transparent;
          border: 1rpx solid #5d5f63;
          color: #5d5f63;
        }
      }
    }
    
    .price2 {
      display: flex;
      align-items: center;
      font-size: 35rpx;
      font-weight: 500;
      color: #5d5f63;
      font-weight: 600;
      
      view {
        margin-right: 20rpx;
      }
      
      text {
        font-weight: 500;
        text-decoration: line-through;
      }
    }
    
    .mcDes {
      width: 246rpx;
      font-size: 22rpx;
      color: #a12406;
      margin-top: 15rpx;
    }
    
    .mc {
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 25rpx;
      color: #686868;
      
      view {
        flex: 2;
        margin-right: 10rpx;
        margin-top: 20rpx;
        
        &:nth-child(2) {
          flex: 1.5;
        }
        
        text {
          color: #428485;
        }
      }
    }
  }
}

/* 商品信息2 */
.info2 {
  padding: 20rpx 25rpx;
  background-color: #fff;
  margin-top: 10rpx;

  .row {
    height: 50rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 24rpx;

    view {
      flex: 2;
      display: flex;
      align-items: center;

      &:nth-child(2) {
        flex: 1.5;
      }

      text {
        color: #696863;
        margin-right: 20rpx;
      }

      .icon {
        font-size: 28rpx;
        margin-left: 8rpx;
      }
    }
  }
}

/* 有效期 */
.expire {
  padding: 0 60rpx;
  background-color: #fff;
  margin-top: 8rpx;

  .date {
    font-size: 32rpx;
    color: #848484;
    font-weight: bold;
  }

  .des {
    font-size: 26rpx;
    color: #b8b7b7;
    margin-top: 10rpx;
  }
}

/* 拼团样式 */
.pt {
  padding: 20rpx 25rpx 30rpx;
  background-color: #fff;

  .top {
    position: relative;
    .flex();
    height: 100rpx;
    background-color: #F7F7F7;

    .col1 {
      .flex(center, start);
      height: 100%;
      font-size: 24rpx;
      color: #0F5760;

      image {
        width: 262rpx;
        height: 100%;
      }

      .save {
        font-size: 27rpx;
        color: #fff;
        line-height: 27rpx;
        padding: 6rpx 14rpx;
        border: 2rpx solid #fff;
        border-radius: 10rpx;
        margin-right: 14rpx;
      }
    }

    .col2 {
      .flex(center, start);
      font-size: 28rpx;
      color: #0F5760;
      background-color: transparent;
      padding: 0;
      border: none;

      text {
        line-height: 1;
      }

      image {
        width: 34rpx;
        height: 34rpx;
        margin: 0 20rpx 0 10rpx;
      }
    }
  }

  .top.xs {
    height: 100rpx;
    background-color: #fff;

    > .col1 {
      .flex(flex-end, start);
      color: #1a1a1a;

      image {
        width: 243rpx;
        height: 54rpx;
        margin-right: 20rpx;
      }

      .save {
        font-size: 27rpx;
        color: #0b4c5d;
        line-height: 27rpx;
        padding: 6rpx 14rpx;
        border: 2rpx solid #126176;
        border-radius: 10rpx;
        margin-right: 14rpx;
      }
    }

    .col2 {
      height: 100%;
      .flex();
      color: #717171;
    }
  }

  .price {
    padding-top: 15rpx;
    .flex();

    .left {
      .flex();

      .col1 {
        font-size: 40rpx;
        color: #179575;
        font-weight: bold;
      }

      .col2 {
        font-size: 32rpx;
        font-weight: bold;
        color: #0FBA90;
        line-height: 1;
        padding: 9rpx 17rpx;
        border: 2rpx solid #0FBA90;
        border-radius: 10rpx;
        margin-left: 13rpx;
      }
    }

    .right {
      .flex(center, start);

      .col1 {
        font-size: 37rpx;
        color: #1f4b38;
        font-weight: bold;
      }

      .col2 {
        .flex(center, start);

        image {
          width: 35rpx;
          height: 35rpx;
          margin-left: 10rpx;
        }
      }
    }
  }

  .price2 {
    font-size: 38rpx;
    color: #858585;
    margin-bottom: 30rpx;

    text {
      text-decoration: line-through;
      margin-right: 50rpx;
    }
  }

  .priceDeposit {
    display: flex;
    align-items: center;
    font-size: 40rpx;
    color: #3B6373;
    font-weight: bold;
  }

  .name {
    .row1 {
      font-size: 35rpx;
      color: #393737;
      font-weight: bold;
      .ellipsis();
    }

    .row2 {
      font-size: 24rpx;
      color: #A4A4A4;
      .ellipsis();
    }

    .row3 {
      display: flex;
      align-items: center;
      margin-top: 10rpx;

      .barCon {
        position: relative;
        width: 220rpx;
        height: 22rpx;
        background: #ffffff;
        border: 2rpx solid #9f2304;
        border-radius: 11rpx;
        overflow: hidden;
        margin-right: 20rpx;

        .bar {
          position: absolute;
          left: 0;
          top: 0;
          height: 22rpx;
          background: #9f2304;
          border-radius: 7rpx;
        }
      }

      .gpNum {
        .flex(center, start);

        .col1 {
          font-size: 37rpx;
          color: #1f4b38;
          font-weight: bold;
        }

        .col2 {
          .flex(center, start);

          image {
            width: 35rpx;
            height: 35rpx;
            margin-left: 10rpx;
          }
        }
      }

      text {
        font-size: 24rpx;
      }
    }

    .row3.xs {
      border-color: #9e1d00;

      .bar {
        background: #39FECE;
      }
    }
  }

  .other {
    margin-top: 20rpx;
    font-size: 25rpx;
    color: #686868;
    .flex(center, space-between);

    .col1 {
      text {
        color: #428485;
      }
    }
  }
}

/* 拼团列表 */
.ptList {
  margin: 20rpx 14rpx 0;

  .title {
    .flex();
    padding: 0 20rpx;
    height: 72rpx;
    background-color: #fff;

    .col1 {
      font-size: 31rpx;
      color: #1f6e57;
      font-weight: bold;
    }

    .col2 {
      font-size: 24rpx;
      color: #4b4949;
    }
  }

  .con {
    .item {
      .flex();
      height: 90rpx;
      background-color: #fff;
      padding: 0 25rpx;
      margin-top: 15rpx;

      .left {
        .flex(center, start);

        image {
          width: 68rpx;
          height: 68rpx;
          border-radius: 50%;
          margin-right: 10rpx;

          &:not(:last-child) {
            filter: blur(1rpx);
          }
        }

        text {
          font-size: 33rpx;
          color: #393737;
        }
      }

      .right {
        button {
          width: 168rpx;
          height: 60rpx;
          line-height: 60rpx;
          text-align: center;
          border: none;
          padding: 0;

          text {
            font-size: 28rpx;
            font-weight: bold;
          }
        }
      }
    }
  }
}

/* 规格选择 */
.standar {
  padding: 20rpx 25rpx;
  background-color: #fff;
  margin-top: 10rpx;

  .row1 {
    .flex(center, space-between);

    .key {
      font-size: 32rpx;
      color: #393737;
    }

    .val {
      font-size: 28rpx;
      color: #393737;
      .flex(center, flex-end);

      &::after {
        content: '';
        width: 0;
        height: 0;
        border-left: 10rpx solid transparent;
        border-right: 10rpx solid transparent;
        border-top: 10rpx solid #999;
        margin-left: 10rpx;
      }
    }
  }

  .row2 {
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
  }
}

/* 商品详情 */
.detail {
  margin-top: 10rpx;
  background-color: #fff;

  .title {
    .flex(center, center);
    height: 100rpx;
    font-size: 32rpx;
    color: #393737;
    font-weight: bold;
    position: relative;

    .line {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translateX(-50%);
      width: 100rpx;
      height: 4rpx;
      background-color: #428485;
      z-index: -1;
    }

    &::before,
    &::after {
      content: '';
      flex: 1;
      height: 2rpx;
      background-color: #e5e5e5;
      margin: 0 20rpx;
    }
  }

  .tip2 {
    padding: 0 25rpx 30rpx;

    .content {
      font-size: 26rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 20rpx;
    }

    .des {
      .name {
        font-size: 28rpx;
        color: #393737;
        font-weight: bold;
        margin-bottom: 10rpx;
      }

      view {
        font-size: 24rpx;
        color: #666;
        line-height: 1.6;
      }
    }
  }

  .con {
    padding: 0 25rpx 30rpx;

    :global(img) {
      max-width: 100% !important;
      height: auto !important;
    }
  }
}

/* 底部操作栏 */
.actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: #fff;
  border-top: 1rpx solid #e5e5e5;
  .flex(center, space-between);
  padding: 0 20rpx;
  z-index: 100;

  .left {
    .flex(center, flex-start);

    button {
      background-color: transparent;
      border: none;
      padding: 0;
      margin: 0;
      .flex(center, center);
      flex-direction: column;

      .text {
        font-size: 20rpx;
        color: #666;
        margin-top: 5rpx;
      }
    }

    > view {
      .flex(center, center);
      flex-direction: column;

      .text {
        font-size: 20rpx;
        color: #666;
        margin-top: 5rpx;
      }
    }
  }

  .right {
    .flex(center, flex-end);

    button {
      width: 206rpx;
      height: 61rpx;
      line-height: 61rpx;
      margin-left: 20rpx;
      text-align: center;
      border: none;
      padding: 0;

      text {
        font-size: 28rpx;
        font-weight: bold;
      }
    }
  }
}

/* 回到顶部 */
.topIcon {
  position: fixed;
  bottom: 200rpx;
  right: 30rpx;
  width: 80rpx;
  height: 80rpx;
  z-index: 99;
}

/* SKU弹窗 */
.sku {
  padding: 30rpx;
  max-height: 80vh;
  overflow-y: auto;

  .skuTop {
    .flex(flex-start, flex-start);
    margin-bottom: 30rpx;
    position: relative;

    .close {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 40rpx;
      color: #999;
    }

    .left {
      width: 160rpx;
      height: 160rpx;
      border-radius: 10rpx;
      margin-right: 20rpx;
    }

    .right {
      flex: 1;

      .skuPrice {
        .skuPriceItem {
          font-size: 36rpx;
          font-weight: bold;
          margin-bottom: 10rpx;

          .label {
            font-size: 24rpx;
            margin-left: 5rpx;
          }
        }

        .mc {
          font-size: 24rpx;
          color: #666;
        }
      }

      .selected {
        font-size: 24rpx;
        color: #666;
        margin-top: 10rpx;
      }
    }
  }

  .skuList {
    margin-bottom: 30rpx;

    .skuItem {
      margin-bottom: 30rpx;

      .skuItemName {
        font-size: 28rpx;
        color: #393737;
        margin-bottom: 15rpx;
      }

      .skuItemCon {
        .flex();
        flex-wrap: wrap;

        > view {
          padding: 15rpx 25rpx;
          border: 2rpx solid #e5e5e5;
          border-radius: 10rpx;
          font-size: 26rpx;
          color: #393737;
          margin-right: 20rpx;
          margin-bottom: 15rpx;

          &.active {
            border-color: #428485;
            color: #428485;
            background-color: #f0f9f9;
          }

          &.disabled {
            color: #ccc;
            border-color: #f0f0f0;
            background-color: #f8f8f8;
          }
        }
      }
    }
  }

  .num {
    .flex(center, space-between);
    margin-bottom: 30rpx;
  }

  .total {
    .flex(center, center);
    font-size: 28rpx;
    color: #393737;
    margin-bottom: 30rpx;
  }

  .skuBtns {
    .flex(center, center);

    button {
      width: 206rpx;
      height: 61rpx;
      line-height: 61rpx;
      margin: 0 10rpx;
      text-align: center;
      border: none;
      padding: 0;

      text {
        font-size: 28rpx;
        font-weight: bold;
      }
    }
  }
}

/* 按钮背景样式 - 从原WePY项目迁移 */
.btn1Bg {
  background: linear-gradient(to bottom, #1ed5ba, #0d5d51);
  border-radius: 8rpx;
  color: #fff;
  border: none;
  padding: 0;

  text {
    color: #fff;
    font-size: 28rpx;
    font-weight: bold;
  }
}

.btn2Bg {
  background: linear-gradient(to bottom, #398DB2, #1E4A5E);
  border-radius: 8rpx;
  color: #fff;
  border: none;
  padding: 0;

  text {
    color: #fff;
    font-size: 28rpx;
    font-weight: bold;
  }
}

.btn3Bg {
  background: linear-gradient(to bottom, #A4E7E1, #68D7CD);
  border-radius: 8rpx;
  color: #273d36;
  border: none;
  padding: 0;

  text {
    color: #273d36;
    font-size: 28rpx;
    font-weight: bold;
  }
}
