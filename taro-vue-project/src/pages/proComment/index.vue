<template>
  <view class="container">
    <view class="header">
      <text class="title">宝贝评价</text>
      <text class="count">共{{ total }}条评论</text>
    </view>
    
    <view class="filter">
      <view 
        class="filter-item" 
        :class="{ active: currentFilter === 'all' }"
        @tap="onFilterChange('all')"
      >
        全部({{ total }})
      </view>
      <view 
        class="filter-item" 
        :class="{ active: currentFilter === 'good' }"
        @tap="onFilterChange('good')"
      >
        好评({{ goodCount }})
      </view>
      <view 
        class="filter-item" 
        :class="{ active: currentFilter === 'medium' }"
        @tap="onFilterChange('medium')"
      >
        中评({{ mediumCount }})
      </view>
      <view 
        class="filter-item" 
        :class="{ active: currentFilter === 'bad' }"
        @tap="onFilterChange('bad')"
      >
        差评({{ badCount }})
      </view>
    </view>

    <view class="list">
      <view class="item" v-for="(item, index) in list" :key="index">
        <view class="user-info">
          <image 
            :src="item.AvatarUrl || '../../static/default.jpg'" 
            class="avatar"
          />
          <view class="user-detail">
            <view class="name">{{ item.Name }}</view>
            <view class="time">{{ item.CreateDate }}</view>
          </view>
          <view class="rating">
            <nut-rate v-model="item.Rating" color="#9F2304" readonly />
          </view>
        </view>
        
        <view class="content">{{ item.Comment }}</view>
        
        <view class="reply" v-if="item.SystemReply">
          <text class="label">商家回复：</text>
          <text class="text">{{ item.SystemReply }}</text>
        </view>
      </view>
    </view>

    <view class="empty" v-if="list.length === 0">
      <text>暂无评论</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Taro, { useRouter } from '@tarojs/taro'
import { NutRate } from '@nutui/nutui-taro'

// 页面参数
const router = useRouter()
const { ProductCode } = router.params

// 响应式数据
const list = ref<any[]>([])
const total = ref(0)
const goodCount = ref(0)
const mediumCount = ref(0)
const badCount = ref(0)
const currentFilter = ref('all')

// 方法
const onFilterChange = (filter: string) => {
  currentFilter.value = filter
  getCommentList()
}

const getCommentList = async () => {
  try {
    Taro.showLoading({ title: '加载中...', mask: true })
    
    // 这里应该调用实际的API获取评论列表
    // 暂时使用模拟数据
    list.value = [
      {
        Name: '用户1',
        AvatarUrl: '',
        CreateDate: '2023-12-01',
        Rating: 5,
        Comment: '商品质量很好，物流很快，满意！',
        SystemReply: '感谢您的好评，我们会继续努力！'
      },
      {
        Name: '用户2',
        AvatarUrl: '',
        CreateDate: '2023-11-28',
        Rating: 4,
        Comment: '整体不错，包装很好，下次还会购买。',
        SystemReply: ''
      }
    ]
    
    total.value = list.value.length
    goodCount.value = list.value.filter(item => item.Rating >= 4).length
    mediumCount.value = list.value.filter(item => item.Rating === 3).length
    badCount.value = list.value.filter(item => item.Rating <= 2).length
    
  } catch (error) {
    console.error('获取评论失败:', error)
    Taro.showToast({ title: '获取评论失败', icon: 'none' })
  } finally {
    Taro.hideLoading()
  }
}

// 生命周期
onMounted(() => {
  getCommentList()
})
</script>

<style lang="less" scoped>
.container {
  background-color: #f6f6f6;
  min-height: 100vh;
}

.header {
  background-color: #fff;
  padding: 30rpx 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .title {
    font-size: 36rpx;
    color: #333;
    font-weight: bold;
  }
  
  .count {
    font-size: 28rpx;
    color: #666;
  }
}

.filter {
  background-color: #fff;
  display: flex;
  padding: 0 20rpx;
  border-top: 1rpx solid #f0f0f0;
  
  .filter-item {
    flex: 1;
    text-align: center;
    padding: 30rpx 0;
    font-size: 28rpx;
    color: #666;
    border-bottom: 3rpx solid transparent;
    
    &.active {
      color: #428485;
      border-bottom-color: #428485;
    }
  }
}

.list {
  margin-top: 10rpx;
  
  .item {
    background-color: #fff;
    padding: 30rpx 20rpx;
    margin-bottom: 10rpx;
    
    .user-info {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      
      .avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        margin-right: 15rpx;
      }
      
      .user-detail {
        flex: 1;
        
        .name {
          font-size: 28rpx;
          color: #333;
          font-weight: bold;
          margin-bottom: 5rpx;
        }
        
        .time {
          font-size: 24rpx;
          color: #999;
        }
      }
      
      .rating {
        :deep(.nut-rate) {
          font-size: 24rpx !important;
        }
      }
    }
    
    .content {
      font-size: 28rpx;
      color: #333;
      line-height: 1.6;
      margin-bottom: 15rpx;
    }
    
    .reply {
      background-color: #f8f8f8;
      padding: 15rpx;
      border-radius: 8rpx;
      font-size: 26rpx;
      color: #666;
      line-height: 1.5;
      
      .label {
        color: #428485;
        font-weight: bold;
      }
    }
  }
}

.empty {
  padding: 100rpx 20rpx;
  text-align: center;
  font-size: 28rpx;
  color: #999;
}
</style>
